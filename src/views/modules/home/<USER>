<template>
  <div class="mobile-app-menu">
    <!-- 系统信息头部 - 压缩高度 -->
    <div class="system-header">
      <div class="system-info">
        <img :src="getAssetsFile(`${systemIcon}.png`)" class="system-avatar" />
        <div class="system-details">
          <h1 class="system-name">{{ systemName }}</h1>
          <p class="system-desc">{{ getSystemDescription() }}</p>
        </div>
      </div>
    </div>

    <!-- 搜索栏 - 压缩间距 -->
    <div class="search-section">
      <n-input v-model:value="searchValue" placeholder="搜索功能..." size="medium" clearable class="search-input">
        <template #prefix>
          <n-icon size="18"><SearchOutline /></n-icon>
        </template>
      </n-input>
    </div>

    <!-- 快捷功能 - 优化布局 -->
    <div class="quick-actions" v-if="quickMenus.length > 0 && !searchValue">
      <div class="section-title">
        <h2>常用功能</h2>
        <span class="section-subtitle">{{ quickMenus.length }}个</span>
      </div>
      <div class="quick-grid">
        <div v-for="(item, index) in quickMenus" :key="index" class="quick-item" @click="handleMenuClick(item)">
          <n-badge :value="getMenuWarnNum(item)" :show-zero="false">
            <div class="quick-content">
              <div class="quick-icon">
                <n-icon size="20">
                  <component :is="getMenuIcon(item)" />
                </n-icon>
              </div>
              <span class="quick-title">{{ getMenuTitle(item) }}</span>
            </div>
          </n-badge>
        </div>
      </div>
    </div>

    <!-- 功能分类 - 改进网格布局 -->
    <div class="menu-categories">
      <div v-for="(category, categoryIndex) in filteredMenuCategories" :key="categoryIndex" class="category-section">
        <div class="section-title">
          <h2>{{ category.title }}</h2>
          <span class="section-subtitle">{{ category.items.length }}个</span>
        </div>

        <!-- 紧凑菜单网格 -->
        <div class="menu-grid">
          <!-- 重要功能（有子菜单的功能） -->
          <div
            v-for="(item, itemIndex) in category.importantItems"
            :key="`important-${itemIndex}`"
            class="menu-card important-card"
            @click="handleMenuClick(item)"
          >
            <n-badge :value="getMenuWarnNum(item)" :show-zero="false" class="card-badge">
              <div class="card-content">
                <div class="card-header">
                  <div class="card-icon">
                    <n-icon size="22">
                      <component :is="getMenuIcon(item)" />
                    </n-icon>
                  </div>
                  <div v-if="hasChildren(item)" class="submenu-indicator">
                    <n-icon size="12"><ChevronForwardOutline /></n-icon>
                  </div>
                </div>
                <div class="card-body">
                  <h3 class="card-title">{{ getMenuTitle(item) }}</h3>
                  <p v-if="hasChildren(item)" class="card-subtitle">{{ getChildrenCount(item) }}个子功能</p>
                  
                  <!-- 恢复子菜单预览标签 -->
                  <div v-if="hasChildren(item)" class="card-preview">
                    <div class="preview-tags">
                      <span
                        v-for="(child, childIndex) in item.children.slice(0, 3)"
                        :key="childIndex"
                        class="preview-tag"
                      >
                        {{ getMenuTitle(child) }}
                      </span>
                      <span v-if="item.children.length > 3" class="preview-more">
                        +{{ item.children.length - 3 }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </n-badge>
          </div>

          <!-- 普通功能（叶子节点） -->
          <div
            v-for="(item, itemIndex) in category.normalCards"
            :key="`normal-${itemIndex}`"
            class="menu-card normal-card"
            @click="handleMenuClick(item)"
          >
            <n-badge :value="getMenuWarnNum(item)" :show-zero="false" class="card-badge">
              <div class="card-content">
                <div class="card-icon">
                  <n-icon size="20">
                    <component :is="getMenuIcon(item)" />
                  </n-icon>
                </div>
                <h3 class="card-title">{{ getMenuTitle(item) }}</h3>
              </div>
            </n-badge>
          </div>
        </div>
      </div>
    </div>

    <!-- iOS风格子菜单模态框 -->
    <n-modal 
      v-model:show="showSubmenuModal" 
      preset="card"
      :title="currentSubmenu?.title"
      :bordered="false"
      :closable="true"
      class="submenu-modal"
      :style="submenuModalStyle"
    >
      <div class="submenu-container">
        <div class="submenu-grid">
          <div
            v-for="(subItem, subIndex) in currentSubmenu?.children"
            :key="subIndex"
            class="submenu-item"
            @click="handleSubmenuClick(subItem)"
          >
            <n-badge :value="getMenuWarnNum(subItem)" :show-zero="false">
              <div class="submenu-content">
                <div class="submenu-icon">
                  <n-icon size="24">
                    <component :is="getMenuIcon(subItem)" />
                  </n-icon>
                </div>
                <span class="submenu-title">{{ getMenuTitle(subItem) }}</span>
                <div v-if="hasChildren(subItem)" class="submenu-arrow">
                  <n-icon size="10"><ChevronForwardOutline /></n-icon>
                </div>
              </div>
            </n-badge>
          </div>
        </div>

        <!-- 三级菜单提示 -->
        <div v-if="hasThirdLevelMenus" class="todo-section">
          <n-alert type="info" :show-icon="false" class="todo-alert">
            <div class="todo-content">
              <strong>开发提醒</strong>
              <p>三级菜单正在开发中...</p>
            </div>
          </n-alert>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { NInput, NIcon, NBadge, NModal, NAlert } from 'naive-ui'
  import {
    SearchOutline,
    ChevronForwardOutline,
    InformationCircleOutline,
    GridOutline,
    DocumentTextOutline,
    SettingsOutline,
    PeopleOutline,
    BarChartOutline,
    CubeOutline,
    CardOutline,
    CalculatorOutline,
    ClipboardOutline,
    HomeOutline,
    StatsChartOutline,
  } from '@vicons/ionicons5'
  import { useSysStore } from '@/store'
  import { useFastArriveStore } from '@/store/fastArrive'
  import { getAssetsFile } from '@/utils'

  // 路由和状态管理
  const router = useRouter()
  const sysStore = useSysStore()
  const arriveStore = useFastArriveStore()

  // 响应式数据
  const searchValue = ref('')
  const showSubmenuModal = ref(false)
  const currentSubmenu = ref<any>(null)

  // 计算属性
  const systemName = computed(() => sysStore.getSystemInfo.systemName || 'HRP系统')
  const systemIcon = computed(() => getSystemIcon(sysStore.getSystemInfo.systemId))
  const menuOptions = computed(() => sysStore.getRoutes)

  // 模态框样式
  const submenuModalStyle = computed(() => ({
    width: '90%',
    maxWidth: '400px',
    maxHeight: '70vh',
    borderRadius: '16px',
  }))

  // 获取系统描述
  const getSystemDescription = () => {
    const descriptions: Record<number, string> = {
      1: '系统核心管理平台',
      2: '人力资源管理系统',
      4: '绩效管理系统',
      5: '采购管理系统',
      6: '物资管理系统',
      11: '资产管理系统',
      16: 'OA办公系统',
    }
    return descriptions[sysStore.getSystemInfo.systemId] || '综合管理平台'
  }

  // 获取系统图标
  const getSystemIcon = (systemId: number) => {
    const iconMap: Record<number, string> = {
      1: 'sys-core',
      2: 'sys-hrm',
      4: 'sys-pms',
      5: 'sys-purms',
      6: 'sys-mmis',
      11: 'sys-ams',
      16: 'sys-oa',
    }
    return iconMap[systemId] || 'sys-core'
  }

  // 获取菜单图标
  const getMenuIcon = (item: any) => {
    const menuName = getMenuTitle(item).toLowerCase()

    // 智能图标匹配
    if (menuName.includes('首页') || menuName.includes('主页')) return HomeOutline
    if (menuName.includes('设置') || menuName.includes('配置')) return SettingsOutline
    if (menuName.includes('人员') || menuName.includes('用户')) return PeopleOutline
    if (menuName.includes('报表') || menuName.includes('统计')) return StatsChartOutline
    if (menuName.includes('图表') || menuName.includes('分析')) return BarChartOutline
    if (menuName.includes('文档') || menuName.includes('档案')) return DocumentTextOutline
    if (menuName.includes('计算') || menuName.includes('核算')) return CalculatorOutline
    if (menuName.includes('清单') || menuName.includes('列表')) return ClipboardOutline
    if (menuName.includes('卡片') || menuName.includes('证件')) return CardOutline
    if (menuName.includes('库存') || menuName.includes('仓库')) return CubeOutline

    return GridOutline
  }

  // 获取菜单标题
  const getMenuTitle = (item: any) => {
    return item.meta?.displayName || item.name || '未命名菜单'
  }

  // 获取菜单警告数量 - 集成arriveStore
  const getMenuWarnNum = (item: any) => {
    // 首先检查item本身是否有warnNum
    if (item.warnNum) {
      return typeof item.warnNum === 'function' ? item.warnNum() : item.warnNum
    }

    // 从arriveStore中查找对应的警告数量
    const arrive = arriveStore.arriveList.find(a => a.path === item.path || a.path === item.key)
    if (arrive) {
      return Number(arrive.warnNum || 0)
    }

    // 如果有子菜单，计算子菜单的总警告数量
    if (item.children && item.children.length > 0) {
      return item.children.reduce((total: number, child: any) => {
        return total + getMenuWarnNum(child)
      }, 0)
    }

    return 0
  }

  // 检查是否有子菜单
  const hasChildren = (item: any) => {
    return item.children && item.children.length > 0
  }

  // 获取子菜单数量
  const getChildrenCount = (item: any) => {
    return item.children ? item.children.length : 0
  }

  // 常用功能（取前6个作为快捷功能，更好利用空间）
  const quickMenus = computed(() => {
    const allMenus = extractAllMenus()
    return allMenus.slice(0, 6) // 改为6个，3x2布局
  })

  // 提取所有菜单项 - 修复重复添加问题
  const extractAllMenus = () => {
    const routes = menuOptions.value
    if (!routes || routes.length === 0) return []

    const allMenuItems: any[] = []
    const processedIds = new Set<string>()

    const extractMenuItems = (items: any[], level = 0) => {
      items.forEach(item => {
        if (item.meta?.hide) return
        
        // 避免重复添加同一个菜单项
        const itemId = item.path || item.name || JSON.stringify(item)
        if (processedIds.has(itemId)) return
        processedIds.add(itemId)

        if (item.children && item.children.length > 0) {
          // 只在第一层添加父菜单，避免重复
          if (level === 0) {
            allMenuItems.push(item)
          }
          // 递归处理子菜单，但不添加父菜单
          extractMenuItems(item.children, level + 1)
        } else if (item.path) {
          // 添加叶子节点
          allMenuItems.push(item)
        }
      })
    }

    routes.forEach(route => {
      if (route.children) {
        extractMenuItems(route.children, 0)
      } else if (route.path && !route.meta?.hide) {
        allMenuItems.push(route)
      }
    })

    return allMenuItems
  }

  // 处理菜单分类 - 移除大卡片，改为重要功能和普通功能
  const filteredMenuCategories = computed(() => {
    const allMenus = extractAllMenus()

    // 过滤搜索结果
    const filteredMenus = searchValue.value
      ? allMenus.filter(item => getMenuTitle(item).toLowerCase().includes(searchValue.value.toLowerCase()))
      : allMenus

    // 重要功能：有子菜单的父级菜单
    const importantItems = filteredMenus.filter(item => hasChildren(item))

    // 普通功能：无子菜单的叶子节点
    const normalCards = filteredMenus.filter(item => !hasChildren(item))

    return [
      {
        title: searchValue.value ? '搜索结果' : '功能模块',
        items: filteredMenus,
        importantItems,
        normalCards,
      },
    ]
  })

  // 检查是否有三级菜单
  const hasThirdLevelMenus = computed(() => {
    return currentSubmenu.value?.children?.some((child: any) => child.children && child.children.length > 0)
  })

  // 处理菜单点击
  const handleMenuClick = (item: any) => {
    if (hasChildren(item)) {
      currentSubmenu.value = {
        title: getMenuTitle(item),
        children: item.children,
      }
      showSubmenuModal.value = true
    } else if (item.path) {
      router.push(item.path)
    }
  }

  // 处理子菜单点击
  const handleSubmenuClick = (subItem: any) => {
    if (hasChildren(subItem)) {
      // TODO: 处理三级菜单
      window.$message?.info('三级菜单功能开发中...')
    } else if (subItem.path) {
      router.push(subItem.path)
      showSubmenuModal.value = false
    }
  }

  // 监听arriveStore变化，实时更新提示数量
  watch(
    () => arriveStore.arriveList,
    () => {
      // 强制更新UI以反映最新的警告数量
      // 这里可以添加额外的逻辑，比如更新特定菜单项的状态
    },
    { deep: true }
  )

  // 生命周期
  onMounted(async () => {
    // 初始化快速到达数据
    try {
      await arriveStore.getFastArrive()
      console.log('移动端菜单：快速到达数据初始化完成', arriveStore.arriveList.length)
    } catch (error) {
      console.error('移动端菜单：快速到达数据初始化失败', error)
    }
  })
</script>

<style scoped>
@reference "tailwindcss";

  /* 系统头部 - 现代化设计 */
  .system-header {
    @apply bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-700 text-white p-5 rounded-b-3xl mb-4 shadow-lg;
    background-image: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  }

  .system-info {
    @apply flex items-center gap-4;
  }

  .system-avatar {
    @apply w-14 h-14 rounded-2xl bg-white/20 p-2.5 backdrop-blur-sm border border-white/20;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  }

  .system-details {
    @apply flex-1;
  }

  .system-name {
    @apply text-xl font-bold mb-1 tracking-wide;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .system-desc {
    @apply text-blue-100 text-sm opacity-90;
  }

  /* 搜索栏 - 现代化设计 */
  .search-section {
    @apply px-4 mb-5;
  }

  .search-input {
    @apply w-full;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  }

  /* 快捷功能 - 现代化卡片设计 */
  .quick-actions {
    @apply px-4 mb-6;
  }

  .section-title {
    @apply flex items-center justify-between mb-4;
  }

  .section-title h2 {
    @apply text-lg font-bold text-gray-900;
  }

  .section-subtitle {
    @apply text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full;
  }

  .quick-grid {
    @apply grid grid-cols-3 gap-3;
  }

  .quick-item {
    @apply bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-all duration-200 border border-gray-100;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }

  .quick-item:hover {
    @apply shadow-md;
    transform: translateY(-1px);
  }

  .quick-content {
    @apply flex flex-col items-center text-center;
  }

  .quick-icon {
    @apply text-blue-600 mb-2;
    filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.15));
  }

  .quick-title {
    @apply text-xs font-semibold text-gray-900 line-clamp-2 leading-tight;
  }

  /* 菜单分类 - 现代化设计 */
  .menu-categories {
    @apply px-4;
  }

  .category-section {
    @apply mb-8;
  }

  .menu-grid {
    @apply grid grid-cols-3 gap-3;
  }

  /* 菜单卡片 - 现代化设计 */
  .menu-card {
    @apply bg-white rounded-2xl shadow-sm active:scale-95 transition-all duration-300 relative border border-gray-100;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 2px 8px rgba(0,0,0,0.06), 0 1px 3px rgba(0,0,0,0.1);
  }

  .menu-card:hover {
    @apply shadow-lg;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1), 0 3px 10px rgba(0,0,0,0.08);
  }

  .important-card {
    @apply p-4 min-h-28;
  }

  .normal-card {
    @apply p-4 aspect-square;
  }

  .card-content {
    @apply h-full flex flex-col;
  }

  .card-header {
    @apply flex items-start justify-between mb-3;
  }

  .card-body {
    @apply flex-1;
  }

  .card-icon {
    @apply text-blue-600;
    filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.15));
  }

  .card-title {
    @apply text-sm font-semibold text-gray-900 line-clamp-2 leading-tight;
  }

  .card-subtitle {
    @apply text-xs text-gray-500 mt-1 font-medium;
  }

  /* 子菜单预览标签 - 现代化设计 */
  .card-preview {
    @apply mt-3;
  }

  .preview-tags {
    @apply flex flex-wrap gap-1.5;
  }

  .preview-tag {
    @apply text-xs bg-gradient-to-r from-gray-100 to-gray-50 px-2.5 py-1 rounded-full text-gray-600 leading-none font-medium border border-gray-200;
  }

  .preview-more {
    @apply text-xs bg-gradient-to-r from-blue-100 to-blue-50 px-2.5 py-1 rounded-full text-blue-600 leading-none font-semibold border border-blue-200;
  }

  .submenu-indicator {
    @apply absolute top-2 right-2 text-gray-400 bg-white rounded-full p-1 shadow-sm;
  }

  .card-badge {
    @apply w-full h-full;
  }

  /* iOS风格子菜单模态框 - 现代化设计 */
  .submenu-container {
    @apply p-4;
  }

  .submenu-grid {
    @apply grid grid-cols-3 gap-3 mb-6;
  }

  .submenu-item {
    @apply bg-gradient-to-br from-gray-50 to-white rounded-2xl p-4 active:scale-95 transition-all duration-200 relative border border-gray-100 shadow-sm;
  }

  .submenu-item:hover {
    @apply shadow-md;
    transform: translateY(-1px);
  }

  .submenu-content {
    @apply flex flex-col items-center text-center;
  }

  .submenu-icon {
    @apply text-blue-600 mb-2;
    filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.15));
  }

  .submenu-title {
    @apply text-xs font-semibold text-gray-900 line-clamp-2 leading-tight;
  }

  .submenu-arrow {
    @apply absolute top-2 right-2 text-gray-400 bg-white rounded-full p-1 shadow-sm;
  }

  /* 开发提示 */
  .todo-section {
    @apply mt-4;
  }

  .todo-alert {
    @apply border-l-4 border-blue-500;
  }

  .todo-content strong {
    @apply text-blue-700 block;
  }

  .todo-content p {
    @apply text-gray-600 mt-1 text-xs;
  }

  /* 徽章样式优化 */
  :deep(.n-badge) {
    .n-badge-sup {
      @apply bg-gradient-to-r from-red-500 to-red-600 border-2 border-white shadow-lg;
      font-weight: 700;
      font-size: 10px;
      min-width: 18px;
      height: 18px;
      line-height: 14px;
      animation: pulse 2s infinite;
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  /* 模态框自定义样式 - 现代化设计 */
  :deep(.n-modal) {
    border-radius: 24px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
  }

  :deep(.n-card-header) {
    padding: 20px 20px 12px 20px;
    font-weight: 700;
    font-size: 18px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
  }

  :deep(.n-card__content) {
    padding: 12px 20px 20px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  /* 整体背景优化 */
  .mobile-app-menu {
    @apply min-h-screen pb-4;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  }
</style>
