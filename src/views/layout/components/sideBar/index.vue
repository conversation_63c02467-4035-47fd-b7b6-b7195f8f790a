<template>
  <div v-if="menuMode">
    <!-- logo -->
    <div v-if="!isGateway" class="sd-logo" @click="goHome">
      <img src="@/assets/images/zjxrmyy.png" style="margin-left: 10px; width: 30px; height: 30px" />
      <span v-if="!collapsed" style="margin-left: 10px" class="sd-logo-name fadenum">{{ systemName }}</span>
    </div>

    <!-- 菜单 -->
    <n-layout has-sider>
      <n-layout-sider
        :collapsed="collapsed"
        :collapsed-width="55"
        bordered
        collapse-mode="width"
        show-trigger
        width="90%"
        @collapse="changeCollapsed(true)"
        @expand="changeCollapsed(false)"
      >
        <!-- 搜索框 -->
        <div v-if="!collapsed" class="menu-search" v-tooltip="'菜单搜索 (' + shortcutText + ')'">
          <n-input
            ref="searchInputRef"
            v-model:value="searchValue"
            clearable
            size="tiny"
            placeholder="菜单搜索"
            :style="{ width: '100%' }"
            @keydown="handleSearchInputKeydown"
          >
            <template #prefix>
              <n-icon size="22" color="#14874a">
                <SearchOutline />
              </n-icon>
            </template>
          </n-input>
          <!-- 添加搜索快捷键提示 -->
          <div v-if="debouncedSearchValue && menuShortcutMap.size > 0" class="shortcut-tip">
            按数字键(1-9)快速导航
          </div>
        </div>
        <n-menu
          v-if="canRenderMenu"
          ref="menuInstRef"
          v-model:value="selectKey"
          :collapsed="collapsed"
          :icon-size="18"
          :indent="14"
          :options="filteredMenuOptions"
          :expanded-keys="expandedKeys"
          :render-icon="renderMenuIcon"
          :render-label="renderMenuLabel"
          @update:expanded-keys="handleUpdateExpanded"
          @collapse="collapsed = true"
          @expand="collapsed = false"
        />
      </n-layout-sider>
    </n-layout>
  </div>
</template>
<script lang="ts">
  import { computed, defineComponent, h, nextTick, onMounted, ref, toRefs, watch, watchEffect, onUnmounted } from 'vue'
  import { RouteRecordRaw, RouterLink, useRouter } from 'vue-router'
  import { MenuOption, NBadge, NInput, NIcon, NPopover } from 'naive-ui'
  import { useSysStore } from '@/store'
  import { HomeOutline as HomeIcon, SearchOutline } from '@vicons/ionicons5'
  import JPGlobal from '@/types/common/jglobal'
  import { addDynamicRoute } from '@/router'
  import { useFastArriveStore } from '@/store/fastArrive'
  import { ElBadge } from 'element-plus'
  import { BookOutline as BookIcon, FolderOpenOutline as FolderOpenOutlineIcon } from '@vicons/ionicons5'

  export default defineComponent({
    components: {
      HomeIcon,
      NInput,
      NIcon,
      SearchOutline,
    },
    setup(props, ctx) {
      const router = useRouter()
      let menuOptions = ref<MenuOption[]>([])
      const sysStore = useSysStore()
      const menuInstRef = ref()
      const searchInputRef = ref()
      const { activeRoutePath } = toRefs(sysStore)
      let selectKey = ref(activeRoutePath)
      let collapsed = ref(false)
      let isGateway = ref(false)
      let systemName = ref<string>('HRP')
      let menuMode = ref(true)

      const getMenuOptions = (routes: RouteRecordRaw[], options: MenuOption[] = []): MenuOption[] => {
        routes.forEach((route: RouteRecordRaw) => {
          if (route.meta?.hide) {
            return
          }
          let option: MenuOption
          if (!route.path) {
            option = {
              label: route.meta?.displayName,
              key: route.name as any,
              icon: JPGlobal.renderIcon(FolderOpenOutlineIcon),
            }
          } else {
            option = {
              label: () =>
                h(
                  RouterLink,
                  {
                    to: {
                      path: route.path,
                    },
                  },
                  { default: () => route.meta?.displayName }
                ),
              name: route.meta?.displayName,
              key: route.path,
              icon: JPGlobal.renderIcon(BookIcon),
            }
          }
          option.systemId = route.meta?.systemId
          option.id = route.meta?.id
          option.parentId = route.meta?.parentId
          if (route.children && route.children.length > 0) {
            option.children = getMenuOptions(route.children)
          }
          options.push(option)
        })

        return options
      }
      // 检测是否为 macOS
      const isMacOS = ref(navigator.platform.toUpperCase().indexOf('MAC') >= 0)

      // 快捷键提示文本
      const shortcutText = computed(() => `快捷键${isMacOS.value ? '⌘K' : 'Ctrl+K'}`)

      // 搜索相关
      const searchValue = ref('')
      const expandedKeys = ref<string[]>([])
      const debouncedSearchValue = ref('')
      const defaultExpandedKeys = ref<string[]>([])

      const canRenderMenu = ref(false)

      // 在这里声明arriveStore，确保在使用前初始化
      const arriveStore = useFastArriveStore()
      const preSysId = ref(0)

      // 监听路由
      watch(
        () => router.currentRoute.value,
        (newValue: any, oldValue: any) => {
          selectKey.value = newValue.path
          isGateway.value = JPGlobal.isGateway(newValue.path)
        },
        { immediate: true }
      )

      // 系统选择变化
      watch(
        () => sysStore.systemInfo,
        systemInfo => {
          systemName.value = systemInfo.systemName
          addDynamicRoute(systemInfo.systemId)
          // 清空搜索框
          searchValue.value = ''
          debouncedSearchValue.value = ''
        },
        {
          immediate: true,
          deep: true,
        }
      )

      // 添加对arriveList的监听
      watch(
        () => arriveStore.arriveList,
        () => {
          if (menuOptions.value.length > 0) {
            updateMenuWarnings()
          }
        },
        { deep: true }
      )

      // 独立出来的更新菜单警告数量的函数
      const updateMenuWarnings = () => {
        // 更新子菜单项的警告数
        menuOptions.value.forEach(item => {
          if (item.children && item.children.length > 0) {
            item.children.forEach(child => {
              const arrive = arriveStore.arriveList.find(a => a.path === child.key)
              if (arrive) {
                child.warnNum = computed(() => Number(arrive.warnNum || 0))
              }
            })

            // 更新父菜单的警告数（基于子菜单的总和）
            item.warnNum = computed(() => {
              return item.children
                .map(child => (typeof child.warnNum === 'function' ? child.warnNum() : child.warnNum) || 0)
                .reduce((prev, curr) => Number(prev) + Number(curr), 0)
            })
          } else {
            // 直接菜单项的警告数
            const arrive = arriveStore.arriveList.find(a => a.path === item.key)
            if (arrive) {
              item.warnNum = computed(() => Number(arrive.warnNum || 0))
            }
          }
        })
      }

      const removeTopMenu = (routes: RouteRecordRaw[]): RouteRecordRaw[] => {
        let options: RouteRecordRaw[] = []
        routes.forEach(r => {
          r.children?.forEach(c => options.push(c))
        })
        return options
      }

      // 初始化左侧菜单
      const initMenu = async (routes: RouteRecordRaw[], remove: boolean = true) => {
        // if (sysStore.systemInfo.systemId == 16) {
        //   oaInitMenu(routes, remove)
        //   return
        // }
        let options = getMenuOptions(routes)
        // @ts-ignore
        menuOptions.value = remove ? removeTopMenu(options) : options

        // 重置菜单项的warnNum
        if (preSysId.value != sysStore.systemInfo.systemId) {
          preSysId.value = sysStore.systemInfo.systemId
          // 使用新的更新函数
          updateMenuWarnings()
        }
      }

      const oaInitMenu = async (routes: RouteRecordRaw[], remove: boolean = true) => {
        let options = getMenuOptions(routes)
        // @ts-ignore
        menuOptions.value = remove ? removeTopMenu(options) : options

        if (preSysId.value != sysStore.systemInfo.systemId) {
          preSysId.value = sysStore.systemInfo.systemId
          console.log(' onMounte darriveStore.arriveList', arriveStore.arriveList)
          // 使用新的更新函数
          updateMenuWarnings()
        }
      }

      // 去首页
      const goHome = () => {
        selectKey.value = ''
        console.log('goHome', arriveStore.Refresh)
        router.push({ path: '/gateway' })
        setTimeout(() => {
          arriveStore.Refresh()
        }, 100)
      }

      // 改变
      const changeCollapsed = (flag: boolean) => {
        collapsed.value = flag
        ctx.emit('changeCollapsed', collapsed.value)
      }

      watch(
        () => sysStore.$state.initOnload,
        (newVal: boolean) => {
          if (newVal) {
            changeCollapsed(sysStore.getCollapsedState)
          }
        }
      )

      // 监听路由
      watch(
        () => sysStore.routes,
        routes => {
          // console.log('routes变化')
          if (preSysId.value != sysStore.systemInfo.systemId) {
            canRenderMenu.value = false
            initMenu(routes, sysStore.getSystemInfo.systemId !== -1)
            canRenderMenu.value = true
          }
          nextTick(() => {
            menuInstRef.value?.showOption(selectKey.value)
          })
        },
        { deep: true }
      )

      // 监听菜单模式
      watch(
        () => sysStore.getMenuMode,
        mode => {
          menuMode.value = mode === 'vertical'
          if (mode === 'vertical') {
            initMenu(sysStore.getRoutes)
          }
        },
        {
          deep: true,
        }
      )

      onMounted(async () => {
        // 添加快捷键事件监听
        window.addEventListener('keydown', handleSearchShortcut)
        // 添加菜单数字快捷键监听
        window.addEventListener('keydown', handleMenuShortcut)
      })

      // 组件卸载时移除事件监听
      onUnmounted(() => {
        window.removeEventListener('keydown', handleSearchShortcut)
        window.removeEventListener('keydown', handleMenuShortcut)
      })

      // 处理搜索快捷键
      const handleSearchShortcut = (event: KeyboardEvent) => {
        // 检查是否按下了 Ctrl+K (Mac上是 Cmd+K)
        const isMacCmd = isMacOS.value && event.metaKey
        const isWinCtrl = !isMacOS.value && event.ctrlKey

        if ((isMacCmd || isWinCtrl) && event.key.toLowerCase() === 'k') {
          event.preventDefault() // 阻止默认行为
          if (!collapsed.value && searchInputRef.value) {
            searchInputRef.value.focus()
          }
        }
      }

      const render = ref(false)

      const renderMenuIcon = (option: MenuOption) => {
        // 渲染图标占位符以保持缩进
        if (option.key === 'sheep-man') return true
        // 返回 falsy 值，不再渲染图标及占位符
        if (option.key === 'food') return null
        //@ts-ignore
        let menuOption = menuOptions.value
          .flatMap(item => {
            if ((item as MenuOption).children) {
              return [...((item as MenuOption).children as MenuOption[]), item as MenuOption]
            } else {
              return item as MenuOption
            }
          })
          .find(item => (item as MenuOption)?.key == option.key)

        // console.log('menuOption', Number((menuOption as MenuOption)?.warnNum || 0))
        const node = h(
          ElBadge,
          {
            showZero: false,
            value: Number((menuOption as MenuOption)?.warnNum || 0),
            max: 999,
            type: 'danger',
            class: 'badge-transition',
          },
          {
            default: option.icon,
          }
        )
        // console.log(node)
        return node
      }

      // 处理菜单展开/收起
      const handleUpdateExpanded = (keys: string[]) => {
        if (debouncedSearchValue.value) {
          // 搜索时的展开操作
          expandedKeys.value = keys
        } else {
          // 正常的展开操作
          defaultExpandedKeys.value = keys
          expandedKeys.value = keys
        }
      }

      // 使用防抖处理搜索值
      watchEffect(() => {
        const handleSearch = JPGlobal.debounce((value: string) => {
          debouncedSearchValue.value = value
          // 搜索时保持原来展开的节点
          if (!value) {
            expandedKeys.value = defaultExpandedKeys.value
          }
        }, 200)
        handleSearch(searchValue.value)
      })

      // 过滤后的菜单选项
      // @ts-ignore
      const filteredMenuOptions = computed(() => {
        if (!debouncedSearchValue.value) {
          // 清除可能存在的旧快捷键标记
          menuOptions.value.forEach(clearShortcuts)
          return menuOptions.value
        }

        const { matchedKeys, expandedKeys: newExpandedKeys } = JPGlobal.searchMenuOptions(
          menuOptions.value,
          debouncedSearchValue.value
        )

        // 更新展开的节点
        expandedKeys.value = newExpandedKeys

        // 创建一个新的菜单树，只包含匹配的项目和它们的父节点
        const result: MenuOption[] = []
        const processedKeys = new Set<string>()
        // 移除旧的 shortcutIndex 和 shortcutMap

        // 清除快捷键标记的辅助函数 (明确类型)
        function clearShortcuts(item: MenuOption | any) {
          // 使用 any 避免过深的类型检查
          delete item._shortcutNumber
          if (item.children) {
            item.children.forEach(clearShortcuts)
          }
        }

        // 处理单个菜单项，限制递归深度
        function processMenuItem(item: MenuOption, depth = 0): MenuOption | null {
          // 清除旧标记
          delete (item as any)._shortcutNumber
          if (depth > 10) return null // 限制递归深度

          const key = item.key as string
          if (processedKeys.has(key)) return null
          processedKeys.add(key)

          // 如果当前节点匹配或在展开路径中
          if (matchedKeys.includes(key) || expandedKeys.value.includes(key)) {
            const newItem = { ...item }

            // 高亮显示名称
            if (typeof newItem.label === 'function') {
              const displayName = (newItem.name as string) || ''
              const originalLabelFn = newItem.label // 保存原始渲染函数

              newItem.label = () => {
                const originalNode = originalLabelFn() // 获取原始节点 (RouterLink)

                // -- 渲染逻辑调整 --
                // 先获取快捷键数字 (如果后处理中已分配)
                const shortcutNum = (newItem as any)._shortcutNumber
                let shortcutNode = null
                if (shortcutNum) {
                  // 使用 h() 创建快捷键 span
                  shortcutNode = h('span', { class: 'menu-shortcut', style: 'margin-right: 5px;' }, `${shortcutNum}`)
                }

                // 使用 h() 创建高亮文本 span (假设 highlightText 返回安全 HTML)
                const highlightedContent = h('span', {
                  innerHTML: JPGlobal.highlightText(displayName, debouncedSearchValue.value),
                })

                // 使用 h() 组合快捷键和高亮文本
                const labelContent = h(
                  'div',
                  { class: 'menu-item-label' }, // 保留容器 div 和 class
                  [shortcutNode, highlightedContent].filter(Boolean) // 过滤掉 null 的 shortcutNode
                )
                // -- 渲染逻辑结束 --

                // 返回新的 RouterLink 节点，将重构后的 labelContent 作为其默认插槽
                return h(
                  originalNode.type, // 保持 RouterLink 类型
                  { ...originalNode.props }, // 保持原始 props
                  { default: () => labelContent } // 使用新的 label 内容
                )
              }
            } else {
              // 对非函数 label 的处理保持不变
              const labelText = String(newItem.label || '')
              newItem.label = () =>
                h('div', {
                  innerHTML: JPGlobal.highlightText(labelText, debouncedSearchValue.value),
                  class: 'menu-item-label',
                })
            }

            if (item.children) {
              const children = item.children
                .map(child => processMenuItem(child, depth + 1))
                .filter((child): child is MenuOption => child !== null)
              if (children.length > 0) {
                newItem.children = children
              } else {
                // 如果子节点都被过滤掉了，确保 children 属性不存在或为空数组
                delete newItem.children
              }
            }
            return newItem
          }
          return null
        }

        // 处理所有顶级菜单项
        menuOptions.value.forEach(item => {
          const processed = processMenuItem(item)
          if (processed) {
            result.push(processed)
          }
        })

        // --- 新增：后处理，遍历 result 树并分配快捷键 ---
        menuShortcutMap.value.clear() // 清空旧的映射
        let currentShortcut = 1

        function traverseAndAssignShortcuts(nodes: MenuOption[]) {
          if (!nodes) return
          for (const node of nodes) {
            if (currentShortcut > 9) return // 最多分配9个

            // 检查是否是匹配的叶子节点
            const nodeKey = node.key as string
            if (!node.children && matchedKeys.includes(nodeKey)) {
              ;(node as any)._shortcutNumber = currentShortcut // 添加临时属性用于渲染
              menuShortcutMap.value.set(currentShortcut, nodeKey) // 更新映射
              currentShortcut++
            }

            // 递归处理子节点 (如果还有快捷键名额)
            if (currentShortcut <= 9 && node.children) {
              traverseAndAssignShortcuts(node.children)
            }
            // 如果当前节点不是叶子节点，也要清除可能残留的标记
            else if (node.children) {
              delete (node as any)._shortcutNumber
            }
          }
        }

        traverseAndAssignShortcuts(result) // 开始分配
        // --- 后处理结束 ---

        // 移除旧的赋值
        // menuShortcutMap.value = shortcutMap // <--- REMOVED

        return result as MenuOption[]
      })

      // 存储当前菜单快捷键映射
      const menuShortcutMap = ref(new Map<number, string>())

      // 新增：处理搜索框按键事件，阻止数字快捷键的默认输入，并处理 Esc 键
      const handleSearchInputKeydown = (event: KeyboardEvent) => {
        // 处理 Esc 键，取消聚焦
        if (event.key === 'Escape') {
          const searchInputEl = searchInputRef.value?.$el?.querySelector('input')
          if (searchInputEl) {
            searchInputEl.blur() // 移除焦点
          }
          searchValue.value = ''

          return // Esc 只处理失焦，不继续后续逻辑
        }

        // --- 以下是处理数字快捷键的逻辑 ---
        // 只在搜索有值时处理数字快捷键
        if (!debouncedSearchValue.value) return

        // 检查是否按下了1-9的数字键
        const key = parseInt(event.key)
        if (!isNaN(key) && key >= 1 && key <= 9) {
          // 检查该快捷键是否有效
          if (menuShortcutMap.value.has(key)) {
            event.preventDefault() // 阻止数字输入到搜索框
          }
        }
      }

      // 处理菜单快捷键 (全局监听，负责导航)
      const handleMenuShortcut = (event: KeyboardEvent) => {
        // 获取 NInput 内部的原生 input 元素并检查是否聚焦
        const searchInputEl = searchInputRef.value?.$el?.querySelector('input')
        if (document.activeElement !== searchInputEl) {
          return // 如果搜索框未聚焦，则不处理
        }

        // 只在搜索有值时处理数字快捷键
        if (!debouncedSearchValue.value) return

        // 检查是否按下了1-9的数字键
        const key = parseInt(event.key)
        if (!isNaN(key) && key >= 1 && key <= 9) {
          const path = menuShortcutMap.value.get(key)
          if (path) {
            event.preventDefault()
            // 导航到对应的菜单
            router.push({ path })
            // 清除搜索
            // searchValue.value = ''
          }
        }
      }

      // 添加renderMenuLabel函数实现
      const renderMenuLabel = (option: MenuOption) => {
        let menuText = ''

        if (typeof option.label === 'function') {
          // 对于RouterLink类型的标签，通过name属性获取文本内容
          menuText = (option.name as string) || ''
        } else {
          // 对于普通文本类型的标签
          menuText = String(option.label || '')
        }

        // 创建内容节点
        let contentNode: any

        // 检查是否有定义快捷键（在搜索状态下）
        const shortcutNum = (option as any)._shortcutNumber
        let shortcutNode = null

        if (shortcutNum) {
          shortcutNode = h('span', { class: 'menu-shortcut', style: 'margin-right: 5px;' }, `${shortcutNum}`)
        }

        if (typeof option.label === 'function') {
          // 对于RouterLink，保持原有的渲染逻辑但增加Popover
          const originalLabelFn = option.label
          const originalNode = originalLabelFn() // 获取原始节点 (RouterLink)

          // 准备label内容
          let labelContent

          if (debouncedSearchValue.value) {
            // 搜索状态下，使用高亮显示
            const highlightedContent = h('span', {
              innerHTML: JPGlobal.highlightText(menuText, debouncedSearchValue.value),
            })

            // 组合快捷键和高亮文本
            labelContent = h('div', { class: 'menu-item-label' }, [shortcutNode, highlightedContent].filter(Boolean))
          } else {
            // 正常状态下，直接显示文本
            labelContent = h('div', { class: 'menu-item-label' }, menuText)
          }

          // 重新构建RouterLink节点
          contentNode = h(originalNode.type, { ...originalNode.props }, { default: () => labelContent })
        } else {
          // 对于普通文本
          if (debouncedSearchValue.value) {
            // 搜索状态下，使用高亮显示
            const highlightedContent = h('span', {
              innerHTML: JPGlobal.highlightText(menuText, debouncedSearchValue.value),
            })

            contentNode = h('div', { class: 'menu-item-label' }, [shortcutNode, highlightedContent].filter(Boolean))
          } else {
            // 正常状态下，直接显示文本
            contentNode = h('div', { class: 'menu-item-label' }, menuText)
          }
        }

        // 使用n-popover包装内容节点
        return h(
          NPopover,
          {
            trigger: 'hover',
            placement: 'right',
            // delay: 500, // 延迟500毫秒显示
            width: 'trigger',
            // 仅在内容被截断时显示popover
            show: menuText.length > 8 ? undefined : false,
          },
          {
            trigger: () => contentNode, // 触发元素是原始内容
            default: () => h('p', menuText), // popover内容是完整文本
          }
        )
      }

      return {
        render,
        canRenderMenu,
        renderMenuIcon,
        menuInstRef,
        searchInputRef,
        selectKey,
        menuOptions,
        changeCollapsed,
        goHome,
        collapsed,
        isGateway,
        systemName,
        searchValue,
        expandedKeys,
        filteredMenuOptions,
        handleUpdateExpanded,
        shortcutText,
        menuMode,
        debouncedSearchValue,
        menuShortcutMap,
        handleSearchInputKeydown,
        renderMenuLabel,
      }
    },
  })
</script>
<style lang="less" scoped>
  @import './index.less';
</style>
